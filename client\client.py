import requests
import json

class PlussClient:
    def __init__(self, server_url='http://localhost:5000'):
        """
        初始化客户端
        :param server_url: 服务端URL地址
        """
        self.server_url = server_url.rstrip('/')
        
    def test_connection(self):
        """测试与服务端的连接"""
        try:
            response = requests.get(f'{self.server_url}/health', timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 连接成功: {data.get('service', 'unknown')}")
                return True
            else:
                print(f"✗ 连接失败: HTTP {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ 连接错误: {e}")
            return False
    
    def get_code(self):
        """从服务端获取代码"""
        try:
            response = requests.get(f'{self.server_url}/api/code', timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✓ 成功获取代码:")
                    for filename, code in data['code'].items():
                        print(f"\n--- {filename} ---")
                        print(code)
                    return data['code']
                else:
                    print(f"✗ 获取代码失败: {data.get('error')}")
                    return None
            else:
                print(f"✗ 请求失败: HTTP {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"✗ 请求错误: {e}")
            return None
    
    def execute_pluss(self, a, b):
        """
        请求服务端执行pluss操作
        :param a: 第一个参数
        :param b: 第二个参数
        :return: 计算结果或None
        """
        try:
            payload = {'a': a, 'b': b}
            response = requests.post(
                f'{self.server_url}/api/pluss',
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data['result']
                    operation = data.get('operation', f'pluss({a}, {b})')
                    print(f"✓ 计算成功: {operation} = {result}")
                    return result
                else:
                    print(f"✗ 计算失败: {data.get('error')}")
                    return None
            else:
                print(f"✗ 请求失败: HTTP {response.status_code}")
                if response.text:
                    try:
                        error_data = response.json()
                        print(f"   错误信息: {error_data.get('error', 'Unknown error')}")
                    except:
                        print(f"   响应内容: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 请求错误: {e}")
            return None
    
    def local_execute_pluss(self, code_dict, a, b):
        """
        本地执行获取到的代码
        :param code_dict: 从服务端获取的代码字典
        :param a: 第一个参数
        :param b: 第二个参数
        :return: 计算结果或None
        """
        try:
            # 创建本地执行环境
            local_env = {}
            
            # 先执行func_2.py (定义plus函数)
            if 'func_2.py' in code_dict:
                exec(code_dict['func_2.py'], local_env)
            
            # 再执行func_1.py (定义pluss函数)
            if 'func_1.py' in code_dict:
                exec(code_dict['func_1.py'], local_env)
            
            # 调用pluss函数
            if 'pluss' in local_env:
                result = local_env['pluss'](a, b)
                print(f"✓ 本地执行成功: pluss({a}, {b}) = {result}")
                return result
            else:
                print("✗ 本地执行失败: 未找到pluss函数")
                return None
                
        except Exception as e:
            print(f"✗ 本地执行错误: {e}")
            return None

def main():
    """主函数 - 演示客户端功能"""
    print("=== Pluss 客户端 ===")
    
    # 创建客户端实例
    client = PlussClient()
    
    # 测试连接
    print("\n1. 测试服务端连接...")
    if not client.test_connection():
        print("请确保服务端已启动 (python server/server.py)")
        return
    
    # 测试参数
    test_a, test_b = 111, 222
    
    # 方式1: 直接请求服务端计算
    print(f"\n2. 请求服务端计算 pluss({test_a}, {test_b})...")
    remote_result = client.execute_pluss(test_a, test_b)
    
    # 方式2: 获取代码并本地执行
    print(f"\n3. 获取代码并本地执行 pluss({test_a}, {test_b})...")
    code = client.get_code()
    if code:
        local_result = client.local_execute_pluss(code, test_a, test_b)
        
        # 比较结果
        if remote_result is not None and local_result is not None:
            if remote_result == local_result:
                print(f"✓ 结果一致: {remote_result}")
            else:
                print(f"✗ 结果不一致: 远程={remote_result}, 本地={local_result}")

if __name__ == '__main__':
    main()
