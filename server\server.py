from flask import Flask, jsonify, request
from func_1 import pluss
import json

app = Flask(__name__)

@app.route('/api/pluss', methods=['POST'])
def api_pluss():
    """
    执行pluss操作的API接口
    接收JSON格式的参数: {"a": number, "b": number}
    返回计算结果: {"result": number, "success": true}
    """
    try:
        data = request.get_json()
        if not data or 'a' not in data or 'b' not in data:
            return jsonify({
                'success': False,
                'error': '参数错误，需要提供a和b两个数值'
            }), 400
        
        a = data['a']
        b = data['b']
        
        # 执行pluss操作
        result = pluss(a, b)
        
        return jsonify({
            'success': True,
            'result': result,
            'operation': f'pluss({a}, {b})'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'计算错误: {str(e)}'
        }), 500

@app.route('/api/code', methods=['GET'])
def get_code():
    """
    获取pluss函数的源代码
    返回函数的源代码字符串
    """
    try:
        # 读取func_1.py和func_2.py的内容
        with open('func_1.py', 'r', encoding='utf-8') as f:
            func1_code = f.read()
        
        with open('func_2.py', 'r', encoding='utf-8') as f:
            func2_code = f.read()
        
        return jsonify({
            'success': True,
            'code': {
                'func_1.py': func1_code,
                'func_2.py': func2_code
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'读取代码失败: {str(e)}'
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'service': 'pluss-calculator'
    })

if __name__ == '__main__':
    print("启动pluss计算服务...")
    print("API接口:")
    print("  POST /api/pluss - 执行pluss计算")
    print("  GET  /api/code  - 获取源代码")
    print("  GET  /health   - 健康检查")
    app.run(host='0.0.0.0', port=5000, debug=True)
